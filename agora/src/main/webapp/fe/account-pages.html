{% extends "fe/include/base.html" %}

{% set activePage = 'PAGES' %}

{% block title %}{{ label('account.pages.title.meta') | raw }} | Agorapp{% endblock %}

{% block pagecss %}
    <link href="{{ contextPath }}/fe/vendor/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">
    <link href="{{ contextPath }}/fe/vendor/choices.js/public/assets/styles/choices.min.css" rel="stylesheet" type="text/css" media="all">
{% endblock %}

{% block content %}
    <div id="pagination" style="display: none;">{{ pagination }}</div>
    <div id="paginationPagesFollow" style="display: none;">{{ paginationPagesFollow }}</div>
    <div id="paginationPagesNotification" style="display: none;">{{ paginationPagesNotification }}</div>
    <a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES') }}?name="></a>
    <a id="accountPagesUri" style="display: none" href="{{ paths('ACCOUNT_PAGES') }}" rel="nofollow"></a>
    <a id="accountInfoEditSaveUri" style="display: none" href="{{ paths('ACCOUNT_INFO_EDIT_SAVE') }}" rel="nofollow"></a>
    <a id="accountRemoveUri" style="display: none" href="{{ paths('ACCOUNT_REMOVE') }}" rel="nofollow"></a>
    <a id="accountDeletedUri" style="display: none" href="{{ paths('ACCOUNT_DELETED') }}" rel="nofollow"></a>
    <a id="pageFollowToggleUri" style="display: none" href="{{ paths('PAGE_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>
    <a id="pageNotificationToggleUri" style="display: none" href="{{ paths('PAGE_NOTIFICATION_TOGGLE') }}" rel="nofollow"></a>
    <a id="pageRemoveUri" style="display: none" href="{{ paths('PAGE_REMOVE') }}" rel="nofollow"></a>
    <a id="accountPageSetPrimaryUri" style="display: none" href="{{ paths('ACCOUNT_PAGE_SET_PRIMARY') }}" rel="nofollow"></a>
    <div id="pages.delete.page" style="display: none">{{ label('pages.delete.page') | raw }}</div>
    <div id="notification.delete.notification" style="display: none">{{ label('notification.delete.notification') | raw }}</div>
    <div id="page.set.primary.success" style="display: none">{{ label('page.set.primary.success') | raw }}</div>
    <div id="page.set.primary.error" style="display: none">{{ label('page.set.primary.error') | raw }}</div>
    <div id="page.set.primary.tooltip" style="display: none">{{ label('page.set.primary.tooltip') | raw }}</div>
    <div id="page.primary.page.tooltip" style="display: none">{{ label('page.primary.page.tooltip') | raw }}</div>

    <!-- Container START -->
    <div class="container">
        <div class="row g-0">

            <!-- Sidenav START -->
            <div class="col-lg-3">
                {% include "fe/include/snippets/sidenav-left.html" %}
            </div>
            <!-- End Col -->

            <!-- Main content START -->
            <div class="col-lg-6 vstack gap-4">
                <!-- Account settings START -->
                <div class="card mb-4 border-lg-lr border-lg-b">

                    <!-- Card header START -->
                    <div class="card-header border-0 pb-0">
                        {% if user.profileType == 'unconfirmed' %}
                        <!-- Alert -->
                        <div class="alert alert-danger text-center card-alert" role="alert">
                            {{ label('account.not.confirm') | raw }} 
                            <a class="alert-link" id="account-confirm-send" href="{{ paths('ACCOUNT_CONFIRM_SEND') }}">
                                {{ label('account.send.link') | raw }}<i class="bi-chevron-right small ms-1"></i>
                            </a>
                        </div>
                        <!-- End Alert -->
                        {% endif %}

                        <div class="row align-items-center">
                            <div class="col-sm-8">
                                <h1 class="h5 card-title mb-1">{{ label('common.pages') | raw }}</h1>
                                <p class="mb-2">{{ label('account.pages.follow.and.own') | raw }}</p>
                            </div>
                            {% if user.profileType != 'unconfirmed' %}
                            <div class="col-sm-4 text-sm-end text-center">
                                <a class="btn btn-primary mt-2 mt-sm-0 w-100 w-md-auto" href="{{ paths('PAGE_ADD') }}">
                                    <i class="fa-solid fa-plus pe-1"></i> {{ label('account.pages.create') | raw }}
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <!-- Card header END -->


                        <!-- Card body START -->
                        <div class="card-body" id="myTabContent">

                            <ul class="nav nav-tabs border-0 justify-content-center" id="myTab" role="tablist">
                              <li class="nav-item" role="presentation">
                                <button class="nav-link btn active" id="followedpages-tab" data-bs-toggle="tab" data-bs-target="#followedpages-tab-pane" type="button" role="tab" aria-controls="followedpages-tab-pane" aria-selected="true">{{ label('account.pages.follow') | raw }}</button>
                              </li>
                              <li class="nav-item" role="presentation">
                                <button class="nav-link btn me-2" id="mypages-tab" data-bs-toggle="tab" data-bs-target="#mypages-tab-pane" type="button" role="tab" aria-controls="mypages-tab-pane" aria-selected="false">{{ label('account.pages.own') | raw }}</button>
                              </li>
                              <li class="nav-item" role="presentation">
                                <button class="nav-link btn me-2" id="mynotifications-tab" data-bs-toggle="tab" data-bs-target="#mynotifications-tab-pane" type="button" role="tab" aria-controls="mynotifications-tab-pane" aria-selected="false">{{ label('account.pages.notifications') | raw }}</button>
                              </li>
                                {% if duplicatePagesAmount >= 2 %}
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link btn me-2" id="myduplicatepages-tab" data-bs-toggle="tab"
                                            data-bs-target="#myduplicatepages-tab-pane" type="button" role="tab"
                                            aria-controls="myduplicatepages-tab-pane" aria-selected="false">
                                        {{ label('common.duplicate.pages') | raw }}
                                    </button>
                                </li>
                                {% endif %}
                            </ul>
                            <div class="tab-content" >
                                <div class="tab-pane fade show active" id="followedpages-tab-pane" role="tabpanel" aria-labelledby="followedpages-tab" tabindex="0">
                                    {% if followPages is not empty %}
                                        <div class="containerPagesFollow">
                                            <div class="pagesFollow">
                                                {% for followPage in followPages %}
                                                    <!-- Connections Item -->
                                                    <div class="card border-bottom">
                                                        <div class="card-header border-0 px-0">
                                                            <div class="d-flex flex-column flex-sm-row align-items-start">
                                                                <div class="d-flex flex-nowrap flex-fill">
                                                                    <div class="avatar me-3 mb-3 mb-md-0">
                                                                        {% if followPage.page.profileImageId is not empty %}
                                                                            <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ followPage.page.profileImageId }}" alt="{{ followPage.page.name }}">
                                                                        {% else %}
                                                                            <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ followPage.page.name }}">
                                                                        {% endif %}
                                                                    </div>
                                                                    <!-- Info -->
                                                                    <div class="w-100">
                                                                        <div class="d-sm-flex align-items-start">
                                                                            <h6 class="mb-0"><a href="{{ paths('PAGE_BASE') }}/{{  followPage.page.identifier }}"  class="pagelink"> {{ followPage.page.name }}</a></h6>
                                                                        </div>
                                                                        <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                                            <li class="nav-item me-2">
                                                                                <i class="bi bi-person-vcard-fill"></i> {{ decode('area', followPage.page.pageType | default('person')) }} 
                                                                           </li>
                                                                            {% if followPage.page.city is not empty %}
                                                                            <li class="nav-item me-2">
                                                                                <i class="bi bi-geo-alt"></i> {{ followPage.page.city }}
                                                                            </li>
                                                                            {% endif %}
                                                                            {% if followPage.followerCount > 0 and (followPage.page.showFollowers == true) %}
                                                                                <li class="nav-item me-2">
                                                                                    <i class="bi bi-people"></i> {{ followPage.followerCount }} {{ label('common.follower') | raw }}
                                                                                </li>
                                                                            {% endif %}
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">
                                                                    <button class="btn btn-danger-soft btn-sm mb-0 me-2 page-add-follow text-nowrap" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ followPage.page.id }}"><i class="bi bi-dash-circle-fill pe-1"></i>{{ label('common.unfollow') | raw }}</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                        <div class="row m-t-sm">
                                            <div class="col-xs-12 text-center">
                                                <div id="loadingFollow" class="spinnerFollow">
                                                    <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row m-t-sm">
                                            <div class="pagerFollow">
                                                {% if resultUrlFollow contains '?' %}
                                                {% if skipFollow > 12 %}
                                                <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow - limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% if loadmoreFollow %}
                                                <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% else %}
                                                {% if skipFollow == 12 %}
                                                <a href="{{ resultUrlFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% endif %}
                                                {% if loadmoreFollow %}
                                                <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% endif %}
                                                {% else %}
                                                {% if skipFollow > 12 %}
                                                <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow - limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% if loadmoreFollow %}
                                                <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% else %}
                                                {% if skipFollow == 12 %}
                                                <a href="{{ resultUrlFollow }}?tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% endif %}
                                                {% if loadmoreFollow %}
                                                <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% endif %}
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% else %}
                                        <div class="alert alert-primary w-100 text-center" role="alert">
                                            {{ label('account.pages.dont.follow') | raw }}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="tab-pane fade" id="mypages-tab-pane" role="tabpanel" aria-labelledby="mypages-tab" tabindex="0">
                                    <!-- Connections Item -->
                                    {% if myPageEntries is not empty %}
                                        <div class="containerMyPages">
                                            <div class="myPages">
                                                {% for mypage in myPageEntries %}
                                                    <div class="card border-bottom">
                                                        <div class="card-header border-0 px-0">
                                                            <div class="d-flex flex-column flex-sm-row align-items-start">
                                                                <div class="d-flex flex-nowrap flex-fill">
                                                                    <div class="avatar me-3 mb-3 mb-md-0">
                                                                        <a href="{{ paths('PAGE_BASE') }}/{{ mypage.page.identifier }}"  class="pagelink">
                                                                            {% if mypage.page.profileImageId is not empty %}
                                                                                <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ mypage.page.profileImageId }}" alt="{{ mypage.page.name }}">
                                                                            {% else %}
                                                                                <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ mypage.page.name }}">
                                                                            {% endif %}
                                                                        </a>
                                                                    </div>
                                                                    <!-- Info -->
                                                                    <div class="w-100">
                                                                        <div class="d-sm-flex align-items-start">
                                                                            <h6 class="mb-0"><a href="{{ paths('PAGE_BASE') }}/{{ mypage.page.identifier }}">{{ mypage.page.name }} </a></h6>
                                                                        </div>
                                                                        <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                                            <li class="nav-item me-2">
                                                                                <i class="bi bi-person-vcard-fill"></i> {{ decode('area', mypage.page.pageType | default('person')) }}
                                                                            </li>
                                                                            {% if mypage.page.city is not empty %}
                                                                            <li class="nav-item me-2">
                                                                                <i class="bi bi-geo-alt"></i> {{ mypage.page.city }}
                                                                            </li>
                                                                            {% endif %}
                                                                            {% if mypage.followerCount > 0 and (mypage.page.showFollowers == true) %}
                                                                                <li class="nav-item me-2">
                                                                                    <i class="bi bi-people"></i> {{ mypage.followerCount }} {{ label('common.follower') | raw }}
                                                                                </li>
                                                                            {% endif %}
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">
                                                                    <button class="btn btn-outline-warning btn-sm mb-0 me-2 page-set-primary text-nowrap"
                                                                            data-page-id="{{ mypage.page.id }}"
                                                                            data-is-primary="{{ mypage.page.isUserPage ? 'true' : 'false' }}"
                                                                            data-bs-toggle="tooltip"
                                                                            data-bs-placement="top"
                                                                            title="{{ mypage.page.isUserPage ? label('page.primary.page.tooltip') : label('page.set.primary.tooltip') | raw }}">
                                                                        <i class="bi {{ mypage.page.isUserPage ? 'bi-star-fill' : 'bi-star' }} pe-1"></i>
                                                                        {% if mypage.page.isUserPage %}
                                                                            {{ label('page.primary.page') | raw }}
                                                                        {% else %}
                                                                            {{ label('page.set.primary') | raw }}
                                                                        {% endif %}
                                                                    </button>
                                                                    <a class="btn btn-primary-soft btn-sm mb-0 me-2 text-nowrap" href="{{ paths('PAGE_EDIT') }}?oid={{ mypage.page.id }}" target="_blank"><i class="bi bi-pencil pe-1"></i> {{ label('common.edit') | raw }}</a>
                                                                    {% if not mypage.page.isUserPage %}
                                                                        <button class="btn btn-danger-soft btn-sm mb-0 me-2 page-remove text-nowrap" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ mypage.page.id }}"><i class="bi bi-trash pe-1"></i>{{ label('common.remove') | raw }}</button>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                        <div class="row m-t-sm">
                                            <div class="col-xs-12 text-center">
                                                <div id="loading" class="spinner">
                                                    <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row m-t-sm">
                                            <div class="pager">
                                                {% if resultUrl contains '?' %}
                                                {% if skip > 12 %}
                                                <a href="{{ resultUrl }}&skip={{ skip - limit }}&limit={{ limit }}&tab=mypages" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% if loadmore %}
                                                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}&tab=mypages" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% else %}
                                                {% if skip == 12 %}
                                                <a href="{{ resultUrl }}&tab=mypages" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% endif %}
                                                {% if loadmore %}
                                                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}&tab=mypages" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% endif %}
                                                {% else %}
                                                {% if skip > 12 %}
                                                <a href="{{ resultUrl }}?skip={{ skip - limit }}&limit={{ limit }}&tab=mypages" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% if loadmore %}
                                                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}&tab=mypages" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% else %}
                                                {% if skip == 12 %}
                                                <a href="{{ resultUrl }}?tab=mypages" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% endif %}
                                                {% if loadmore %}
                                                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}&tab=mypages" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% endif %}
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% else %}
                                        {% if user.profileType != 'unconfirmed' %}
                                            <div class="alert alert-primary w-100 text-center" role="alert">
                                                {{ label('account.pages.create.havent') | raw }} <a class="fw-bold" href="{{ paths('PAGE_ADD') }}">{{ label('account.pages.create.first') | raw }}</a>
                                            </div>
                                        {% else %}
                                            <div class="alert alert-primary w-100 text-center" role="alert">
                                                {{ label('account.pages.must.confirm') | raw }}
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                </div>                                
                                <div class="tab-pane fade" id="mynotifications-tab-pane" role="tabpanel" aria-labelledby="mynotifications-tab" tabindex="0">
                                    <!-- Connections Item -->
                                    {% if notificationPages is not empty %}
                                        <div class="containerMyNotifications">
                                            <div class="myNotifications">
                                                {% for notificationpage in notificationPages %}
                                                    <div class="card border-bottom">
                                                        <div class="card-header border-0 px-0">
                                                            <div class="d-flex flex-column flex-sm-row align-items-start">
                                                                <div class="d-flex flex-nowrap flex-fill">
                                                                    <div class="avatar me-3 mb-3 mb-md-0">
                                                                        <a href="{{ paths('PAGE_BASE') }}/{{ notificationpage.page.identifier }}"  class="pagelink">
                                                                            {% if notificationpage.page.profileImageId is not empty %}
                                                                                <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ notificationpage.page.profileImageId }}" alt="{{ notificationpage.page.name }}">
                                                                            {% else %}
                                                                                <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ notificationpage.page.name }}">
                                                                            {% endif %}
                                                                        </a>
                                                                    </div>
                                                                    <!-- Info -->
                                                                    <div class="w-100">
                                                                        <div class="d-sm-flex align-items-start">
                                                                            <h6 class="mb-0"><a href="{{ paths('PAGE_BASE') }}/{{ notificationpage.page.identifier }}">{{ notificationpage.page.name }} </a></h6>
                                                                        </div>
                                                                        <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                                            <li class="nav-item">
                                                                                <i class="bi bi-person-vcard-fill pe-1"></i> {{ decode('area', notificationpage.page.pageType | default('person')) }}
                                                                            </li>
                                                                            {% if notificationpage.page.city is not empty %}
                                                                            <li class="nav-item">
                                                                                <i class="bi bi-geo-alt pe-1"></i> {{ notificationpage.page.city }}
                                                                            </li>
                                                                            {% endif %}
                                                                            {% if notificationpage.followerCount > 0 and (notificationpage.page.showFollowers == true) %}
                                                                                <li class="nav-item">
                                                                                    <i class="bi bi-people pe-1"></i> {{ notificationpage.followerCount }} {{ label('common.follower') | raw }}
                                                                                </li>
                                                                            {% endif %}
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">
                                                                    <button class="btn btn-danger-soft btn-sm mb-0 me-2 page-remove-notification text-nowrap" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ notificationpage.page.id }}"><i class="bi bi-trash pe-1"></i>{{ label('common.remove.notification') | raw }}</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                        <div class="row m-t-sm">
                                            <div class="col-xs-12 text-center">
                                                <div id="loadingNotification" class="spinnerNotification">
                                                    <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row m-t-sm">
                                            <div class="pager">
                                                {% if resultUrl contains '?' %}
                                                {% if skip > 12 %}
                                                <a href="{{ resultUrl }}&skip={{ skip - limit }}&limit={{ limit }}&tab=mynotifications" class="pager__prevNotification" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% if loadmore %}
                                                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}&tab=mynotifications" class="pager__nextNotification" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% else %}
                                                {% if skip == 12 %}
                                                <a href="{{ resultUrl }}&tab=mynotifications" class="pager__prevNotification" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% endif %}
                                                {% if loadmore %}
                                                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}&tab=mynotifications" class="pager__nextNotification" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% endif %}
                                                {% else %}
                                                {% if skip > 12 %}
                                                <a href="{{ resultUrl }}?skip={{ skip - limit }}&limit={{ limit }}&tab=mynotifications" class="pager__prevNotification" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% if loadmore %}
                                                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}&tab=mynotifications" class="pager__nextNotification" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% else %}
                                                {% if skip == 12 %}
                                                <a href="{{ resultUrl }}?tab=mynotifications" class="pager__prevNotification" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                                {% endif %}
                                                {% if loadmore %}
                                                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}&tab=mynotifications" class="pager__nextNotification" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                                {% endif %}
                                                {% endif %}
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% else %}
                                        <div class="alert alert-primary w-100 text-center" role="alert">
                                            {{ label('account.pages.notification.havent') | raw }}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="tab-pane fade" id="myduplicatepages-tab-pane" role="tabpanel" aria-labelledby="myduplicatepages-tab" tabindex="0">
                                    <!-- Connections Item -->
                                    {% if duplicatePagesAmount >= 2 %}
                                    <div class="d-flex justify-content-center align-items-center">
                                        <a href="{{ paths('PAGE_DIFF') }}" class="btn btn-info btn-sm mb-0 me-2 text-nowrap">
                                            <i class="bi bi-pencil pe-1"></i> {{ label('common.merge.pages') | raw }}
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>


                        </div>
                        <!-- Card body END -->

                </div>
                <!-- Account settings END -->

            </div>
        </div> <!-- Row END -->
    </div>
    <!-- Container END -->

{% endblock %}

{% block pagescripts %}
    <script src="{{ contextPath }}/fe/vendor/choices.js/public/assets/scripts/choices.min.js"></script>
    <script src="{{ contextPath }}/fe/vendor/slim/slim.kickstart.min.js"></script>
    <script src="{{ contextPath }}/fe/js/pages/account-pages.js?{{ buildNumber }}"></script>
{% endblock %}
